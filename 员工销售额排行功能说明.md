# 员工销售额排行功能说明

## 功能概述

本功能实现了使用 `employeeSalesStatisticsService` 按 `statType` 和 `userIds` 查询员工销售排行，同时满足条件 `dept_id is null`，并按 `total_sales` 字段从大到小排序，取前6个，输出 `userId` 和个人总销售额，然后将 `userId` 转换成 `nickName`。

## 实现文件

### 1. 控制器
- **文件**: `ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/EmployeeDataRankingListController.java`
- **接口**: `GET /employee_backend_data/querySalesRankList?statType={statType}`
- **功能**: 查询员工销售额排行前6名

### 2. Service层
- **接口**: `ruoyi-system/src/main/java/com/ruoyi/system/service/IEmployeeSalesStatisticsService.java`
- **实现**: `ruoyi-system/src/main/java/com/ruoyi/system/service/impl/EmployeeSalesStatisticsServiceImpl.java`
- **新增方法**: `querySalesRankingByUserIds(...)`

### 3. Mapper层
- **接口**: `ruoyi-system/src/main/java/com/ruoyi/system/mapper/EmployeeSalesStatisticsMapper.java`
- **XML**: `ruoyi-system/src/main/resources/mapper/system/EmployeeSalesStatisticsMapper.xml`
- **新增方法**: `querySalesRankingByUserIds(...)`

## 核心SQL实现

```sql
SELECT 
    id, stat_type, dept_id, user_id, total_sales, total_orders, avg_order_amount, 
    growth_rate, create_time, update_time, leader_id, leader_nick_name, 
    parent_leader_id, parent_leader_nick_name
FROM `wendao101-order`.employee_sales_statistics
WHERE stat_type = #{statType}
AND dept_id IS NULL
<if test="userIds != null and userIds.size() > 0">
    AND user_id IN
    <foreach collection="userIds" item="userId" open="(" separator="," close=")">
        #{userId}
    </foreach>
</if>
ORDER BY total_sales DESC
<if test="limit != null and limit > 0">
    LIMIT #{limit}
</if>
```

## 接口参数

### 请求参数
- **statType** (String): 统计类型，支持以下值：
  - `daily`: 日统计
  - `weekly`: 周统计
  - `monthly`: 月统计
  - `yearly`: 年统计

### 查询条件
- **dept_id IS NULL**: 只查询个人销售数据，排除部门汇总数据
- **员工范围**: 商务部（deptId=104）下的所有子部门员工，排除部门名称为"商务部"的用户
- **排序**: 按 `total_sales` 字段降序排列
- **限制**: 返回前6名

## 返回数据结构

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "rankingList": [
      {
        "userId": 123,
        "nickName": "张三",
        "totalSales": 150000.00
      },
      {
        "userId": 124,
        "nickName": "李四",
        "totalSales": 120000.00
      },
      {
        "userId": 125,
        "nickName": "王五",
        "totalSales": 100000.00
      },
      {
        "userId": 126,
        "nickName": "赵六",
        "totalSales": 80000.00
      },
      {
        "userId": 127,
        "nickName": "孙七",
        "totalSales": 60000.00
      },
      {
        "userId": 128,
        "nickName": "周八",
        "totalSales": 40000.00
      }
    ],
    "statType": "monthly",
    "totalCount": 6
  }
}
```

## 数据字段说明

### rankingList（排行列表）
- `userId`: 员工用户ID
- `nickName`: 员工昵称
- `totalSales`: 个人总销售额

### 其他字段
- `statType`: 统计类型
- `totalCount`: 返回的记录数量

## 使用示例

### 1. 查询月度销售排行
```bash
curl -X GET "http://localhost:8080/employee_backend_data/querySalesRankList?statType=monthly"
```

### 2. 查询日销售排行
```bash
curl -X GET "http://localhost:8080/employee_backend_data/querySalesRankList?statType=daily"
```

### 3. 查询周销售排行
```bash
curl -X GET "http://localhost:8080/employee_backend_data/querySalesRankList?statType=weekly"
```

### 4. 查询年度销售排行
```bash
curl -X GET "http://localhost:8080/employee_backend_data/querySalesRankList?statType=yearly"
```

## 技术特点

### 1. SQL优化
- 使用 `ORDER BY total_sales DESC` 直接在数据库层面完成排序
- 使用 `LIMIT` 直接限制返回数量，减少数据传输
- 使用 `IN` 查询批量匹配用户ID，避免多次查询
- 使用 `dept_id IS NULL` 条件过滤个人销售数据

### 2. 数据处理优化
- 一次SQL查询完成所有统计和排序
- 在应用层进行 userId 到 nickName 的转换
- 利用 Stream API 进行高效的数据匹配

### 3. 业务逻辑
- 区分个人销售数据（dept_id IS NULL）和部门汇总数据
- 支持多种统计类型的灵活查询
- 自动过滤商务部本身，只统计子部门员工

## 数据表结构

### employee_sales_statistics 表
- `id`: 统计ID
- `stat_type`: 统计类型（daily/weekly/monthly/yearly）
- `dept_id`: 部门ID（NULL表示个人数据，-1表示部门汇总）
- `user_id`: 商务专员ID
- `total_sales`: 总销售额
- `total_orders`: 总订单数
- `avg_order_amount`: 平均订单金额
- `growth_rate`: 增长率
- `create_time`: 创建时间
- `update_time`: 更新时间

## 测试验证

### 1. 接口测试
使用上述curl命令测试各种统计类型的查询。

### 2. SQL验证
可以使用 `test_employee_sales_ranking_query.sql` 文件中的SQL语句验证数据的正确性。

### 3. 数据验证
- 验证返回的员工都属于商务部子部门
- 验证销售额按降序排列
- 验证只查询 dept_id IS NULL 的记录
- 验证 userId 到 nickName 的转换正确性

## 注意事项

1. **数据范围**: 只查询 `dept_id IS NULL` 的个人销售数据
2. **员工范围**: 只统计商务部子部门的员工，排除"商务部"本身
3. **统计类型**: 支持 daily、weekly、monthly、yearly 四种类型
4. **排行数量**: 固定返回前6名，如果实际员工数少于6个，返回实际数量
5. **空值处理**: 如果某个员工没有销售记录，不会出现在排行中
6. **数据同步**: 依赖于 employee_sales_statistics 表的数据及时更新

## 扩展功能

1. **可配置排行数量**: 可以将LIMIT参数化，支持动态设置返回数量
2. **部门维度排行**: 可以扩展为按部门统计排行
3. **多维度排序**: 可以支持按订单数、平均订单金额等其他字段排序
4. **缓存优化**: 可以为热点查询添加Redis缓存
5. **实时数据**: 可以结合实时数据计算当前排行
