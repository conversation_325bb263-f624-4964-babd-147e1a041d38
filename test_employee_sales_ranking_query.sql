-- 测试员工销售额排行查询SQL
-- 这个SQL用于验证我们在EmployeeDataRankingListController中实现的销售排行逻辑

-- 1. 查询所有员工在指定统计类型下的销售排行（示例：monthly数据）
SELECT 
    id, stat_type, dept_id, user_id, total_sales, total_orders, avg_order_amount, 
    growth_rate, create_time, update_time, leader_id, leader_nick_name, 
    parent_leader_id, parent_leader_nick_name
FROM `wendao101-order`.employee_sales_statistics
WHERE stat_type = 'monthly'  -- 统计类型
AND dept_id IS NULL          -- 条件：dept_id is null
AND user_id IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10)  -- 示例用户ID列表
ORDER BY total_sales DESC    -- 按total_sales字段从大到小排序
LIMIT 6;                     -- 取前6个

-- 2. 查询daily统计类型的销售排行
SELECT 
    id, stat_type, dept_id, user_id, total_sales, total_orders, avg_order_amount, 
    growth_rate, create_time, update_time, leader_id, leader_nick_name, 
    parent_leader_id, parent_leader_nick_name
FROM `wendao101-order`.employee_sales_statistics
WHERE stat_type = 'daily'
AND dept_id IS NULL
AND user_id IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10)
ORDER BY total_sales DESC
LIMIT 6;

-- 3. 查询weekly统计类型的销售排行
SELECT 
    id, stat_type, dept_id, user_id, total_sales, total_orders, avg_order_amount, 
    growth_rate, create_time, update_time, leader_id, leader_nick_name, 
    parent_leader_id, parent_leader_nick_name
FROM `wendao101-order`.employee_sales_statistics
WHERE stat_type = 'weekly'
AND dept_id IS NULL
AND user_id IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10)
ORDER BY total_sales DESC
LIMIT 6;

-- 4. 查询yearly统计类型的销售排行
SELECT 
    id, stat_type, dept_id, user_id, total_sales, total_orders, avg_order_amount, 
    growth_rate, create_time, update_time, leader_id, leader_nick_name, 
    parent_leader_id, parent_leader_nick_name
FROM `wendao101-order`.employee_sales_statistics
WHERE stat_type = 'yearly'
AND dept_id IS NULL
AND user_id IN (1, 2, 3, 4, 5, 6, 7, 8, 9, 10)
ORDER BY total_sales DESC
LIMIT 6;

-- 5. 验证数据完整性 - 查看所有员工的销售统计情况
SELECT 
    stat_type,
    dept_id,
    user_id,
    total_sales,
    total_orders,
    avg_order_amount,
    create_time,
    update_time
FROM `wendao101-order`.employee_sales_statistics
WHERE dept_id IS NULL
ORDER BY stat_type, total_sales DESC;

-- 6. 查看不同dept_id的数据分布
SELECT 
    dept_id,
    COUNT(*) as count,
    SUM(total_sales) as total_sales_sum,
    AVG(total_sales) as avg_sales,
    CASE 
        WHEN dept_id IS NULL THEN '个人销售'
        WHEN dept_id = -1 THEN '部门销售'
        ELSE CONCAT('部门ID:', dept_id)
    END as dept_desc
FROM `wendao101-order`.employee_sales_statistics 
GROUP BY dept_id
ORDER BY dept_id;

-- 7. 查看最新的销售记录（用于验证数据时效性）
SELECT 
    id,
    stat_type,
    dept_id,
    user_id,
    total_sales,
    total_orders,
    update_time
FROM `wendao101-order`.employee_sales_statistics 
WHERE dept_id IS NULL
ORDER BY update_time DESC 
LIMIT 20;

-- 8. 测试动态参数查询（模拟MyBatis的foreach）
-- 这个查询展示了如何处理用户ID列表的IN查询
SELECT 
    user_id,
    stat_type,
    total_sales,
    total_orders
FROM `wendao101-order`.employee_sales_statistics
WHERE stat_type = 'monthly'
AND dept_id IS NULL
AND user_id IN (
    SELECT DISTINCT user_id 
    FROM `wendao101-order`.employee_sales_statistics 
    WHERE dept_id IS NULL 
      AND total_sales > 0
    LIMIT 10  -- 模拟获取前10个有销售额的员工
)
ORDER BY total_sales DESC
LIMIT 6;

-- 9. 验证用户ID和昵称的对应关系（需要关联sys_user表）
-- 注意：这个查询需要根据实际的用户表结构调整
SELECT 
    ess.user_id,
    ess.stat_type,
    ess.total_sales,
    su.nick_name,
    su.user_name
FROM `wendao101-order`.employee_sales_statistics ess
LEFT JOIN sys_user su ON ess.user_id = su.user_id
WHERE ess.stat_type = 'monthly'
AND ess.dept_id IS NULL
AND ess.total_sales > 0
ORDER BY ess.total_sales DESC
LIMIT 10;

-- 10. 统计各个统计类型的数据量
SELECT 
    stat_type,
    COUNT(*) as record_count,
    COUNT(DISTINCT user_id) as unique_users,
    SUM(total_sales) as total_sales_sum,
    AVG(total_sales) as avg_sales,
    MAX(total_sales) as max_sales,
    MIN(total_sales) as min_sales
FROM `wendao101-order`.employee_sales_statistics
WHERE dept_id IS NULL
GROUP BY stat_type
ORDER BY stat_type;
