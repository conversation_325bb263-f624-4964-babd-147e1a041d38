package com.ruoyi.system.service;

import java.math.BigDecimal;
import java.util.List;
import com.ruoyi.system.domain.EmployeeSalesStatistics;

/**
 * 员工销售数据统计Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface IEmployeeSalesStatisticsService 
{
    /**
     * 查询员工销售数据统计
     * 
     * @param id 员工销售数据统计主键
     * @return 员工销售数据统计
     */
    public EmployeeSalesStatistics selectEmployeeSalesStatisticsById(Long id);

    /**
     * 查询员工销售数据统计列表
     * 
     * @param employeeSalesStatistics 员工销售数据统计
     * @return 员工销售数据统计集合
     */
    public List<EmployeeSalesStatistics> selectEmployeeSalesStatisticsList(EmployeeSalesStatistics employeeSalesStatistics);

    /**
     * 新增员工销售数据统计
     * 
     * @param employeeSalesStatistics 员工销售数据统计
     * @return 结果
     */
    public int insertEmployeeSalesStatistics(EmployeeSalesStatistics employeeSalesStatistics);

    /**
     * 修改员工销售数据统计
     * 
     * @param employeeSalesStatistics 员工销售数据统计
     * @return 结果
     */
    public int updateEmployeeSalesStatistics(EmployeeSalesStatistics employeeSalesStatistics);

    /**
     * 批量删除员工销售数据统计
     * 
     * @param ids 需要删除的员工销售数据统计主键集合
     * @return 结果
     */
    public int deleteEmployeeSalesStatisticsByIds(Long[] ids);

    /**
     * 删除员工销售数据统计信息
     * 
     * @param id 员工销售数据统计主键
     * @return 结果
     */
    public int deleteEmployeeSalesStatisticsById(Long id);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByUserId(Long userId,String statType);

    int queryHasTeam(Long userId);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByParentLeaderId(Long userId, String statType);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByParentLeaderIdAndDeptId(Long userId, String statType);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByLeaderId(Long userId, String statType);

    EmployeeSalesStatistics selectEmployeeSalesStatisticsByLeaderIdAndDeptId(Long userId, String statType);

    /**
     * 根据用户ID列表和统计类型查询销售总额
     * @param userIds 用户ID列表
     * @param statType 统计类型
     * @return 销售总额
     */
    BigDecimal sumTotalSalesByUserIdsAndStatType(List<Long> userIds, String statType);

    /**
     * 根据统计类型和用户ID列表查询销售排行，条件dept_id is null，按total_sales降序排列
     * @param statType 统计类型
     * @param userIds 用户ID列表
     * @param limit 限制返回数量
     * @return 销售排行列表
     */
    List<EmployeeSalesStatistics> querySalesRankingByUserIds(String statType, List<Long> userIds, Integer limit);
}