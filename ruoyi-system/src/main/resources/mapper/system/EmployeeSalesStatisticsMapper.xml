<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.EmployeeSalesStatisticsMapper">
    
    <resultMap type="EmployeeSalesStatistics" id="EmployeeSalesStatisticsResult">
        <result property="id"    column="id"    />
        <result property="statType"    column="stat_type"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userId"    column="user_id"    />
        <result property="totalSales"    column="total_sales"    />
        <result property="totalOrders"    column="total_orders"    />
        <result property="avgOrderAmount"    column="avg_order_amount"    />
        <result property="growthRate"    column="growth_rate"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="leaderId"    column="leader_id"    />
        <result property="leaderNickName"    column="leader_nick_name"    />
        <result property="parentLeaderId"    column="parent_leader_id"    />
        <result property="parentLeaderNickName"    column="parent_leader_nick_name"    />
    </resultMap>

    <sql id="selectEmployeeSalesStatisticsVo">
        select id, stat_type, dept_id, user_id, total_sales, total_orders, avg_order_amount, growth_rate, create_time, update_time, leader_id, leader_nick_name, parent_leader_id, parent_leader_nick_name from `wendao101-order`.employee_sales_statistics
    </sql>

    <select id="selectEmployeeSalesStatisticsList" parameterType="EmployeeSalesStatistics" resultMap="EmployeeSalesStatisticsResult">
        <include refid="selectEmployeeSalesStatisticsVo"/>
        <where>  
            <if test="statType != null  and statType != ''"> and stat_type = #{statType}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="totalSales != null "> and total_sales = #{totalSales}</if>
            <if test="totalOrders != null "> and total_orders = #{totalOrders}</if>
            <if test="avgOrderAmount != null "> and avg_order_amount = #{avgOrderAmount}</if>
            <if test="growthRate != null "> and growth_rate = #{growthRate}</if>
            <if test="leaderId != null "> and leader_id = #{leaderId}</if>
            <if test="leaderNickName != null  and leaderNickName != ''"> and leader_nick_name like concat('%', #{leaderNickName}, '%')</if>
            <if test="parentLeaderId != null "> and parent_leader_id = #{parentLeaderId}</if>
            <if test="parentLeaderNickName != null  and parentLeaderNickName != ''"> and parent_leader_nick_name like concat('%', #{parentLeaderNickName}, '%')</if>
        </where>
    </select>
    
    <select id="selectEmployeeSalesStatisticsById" parameterType="Long" resultMap="EmployeeSalesStatisticsResult">
        <include refid="selectEmployeeSalesStatisticsVo"/>
        where id = #{id}
    </select>
    <select id="selectEmployeeSalesStatisticsByUserId" resultMap="EmployeeSalesStatisticsResult">
        <include refid="selectEmployeeSalesStatisticsVo"/>
        where user_id = #{userId} and stat_type = #{statType} order by id desc limit 1
    </select>
    <select id="queryHasTeam" resultType="java.lang.Integer">
        select count(1) from `wendao101-order`.employee_sales_statistics where leader_id = #{userId} or parent_leader_id = #{userId}
    </select>
    <select id="selectEmployeeSalesStatisticsByParentLeaderId" resultMap="EmployeeSalesStatisticsResult">
        select id, stat_type, dept_id, user_id, sum(total_sales) as total_sales, sum(total_orders) as total_orders, COALESCE(SUM(total_sales) / NULLIF(SUM(total_orders), 0), 0) as avg_order_amount, growth_rate, create_time, update_time, leader_id, leader_nick_name, parent_leader_id, parent_leader_nick_name from `wendao101-order`.employee_sales_statistics
        where parent_leader_id = #{userId} and stat_type = #{statType} and dept_id is null
    </select>
    <select id="selectEmployeeSalesStatisticsByParentLeaderIdAndDeptId" resultMap="EmployeeSalesStatisticsResult">
        select id, stat_type, dept_id, user_id, sum(total_sales) as total_sales, sum(total_orders) as total_orders, COALESCE(SUM(total_sales) / NULLIF(SUM(total_orders), 0), 0) as avg_order_amount, growth_rate, create_time, update_time, leader_id, leader_nick_name, parent_leader_id, parent_leader_nick_name from `wendao101-order`.employee_sales_statistics
        where parent_leader_id = #{userId} and stat_type = #{statType} and dept_id = -1
    </select>
    <select id="selectEmployeeSalesStatisticsByLeaderId" resultMap="EmployeeSalesStatisticsResult">
        select id, stat_type, dept_id, user_id, sum(total_sales) as total_sales, sum(total_orders) as total_orders, COALESCE(SUM(total_sales) / NULLIF(SUM(total_orders), 0), 0) as avg_order_amount, growth_rate, create_time, update_time, leader_id, leader_nick_name, parent_leader_id, parent_leader_nick_name from `wendao101-order`.employee_sales_statistics
        where leader_id = #{userId} and stat_type = #{statType} and dept_id is null
    </select>
    <select id="selectEmployeeSalesStatisticsByLeaderIdAndDeptId" resultMap="EmployeeSalesStatisticsResult">
        select id, stat_type, dept_id, user_id, sum(total_sales) as total_sales, sum(total_orders) as total_orders, COALESCE(SUM(total_sales) / NULLIF(SUM(total_orders), 0), 0) as avg_order_amount, growth_rate, create_time, update_time, leader_id, leader_nick_name, parent_leader_id, parent_leader_nick_name from `wendao101-order`.employee_sales_statistics
        where leader_id = #{userId} and stat_type = #{statType} and dept_id = -1
    </select>

    <!-- 根据用户ID列表和统计类型查询销售总额 -->
    <select id="sumTotalSalesByUserIdsAndStatType" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(total_sales), 0)
        FROM `wendao101-order`.employee_sales_statistics
        WHERE stat_type = #{statType}
        AND dept_id IS NULL
        <if test="userIds != null and userIds.size() > 0">
            AND user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </select>

    <!-- 根据统计类型和用户ID列表查询销售排行，条件dept_id is null，按total_sales降序排列 -->
    <select id="querySalesRankingByUserIds" resultMap="EmployeeSalesStatisticsResult">
        SELECT
            id, stat_type, dept_id, user_id, total_sales, total_orders, avg_order_amount,
            growth_rate, create_time, update_time, leader_id, leader_nick_name,
            parent_leader_id, parent_leader_nick_name
        FROM `wendao101-order`.employee_sales_statistics
        WHERE stat_type = #{statType}
        AND dept_id IS NULL
        <if test="userIds != null and userIds.size() > 0">
            AND user_id IN
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        ORDER BY total_sales DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <insert id="insertEmployeeSalesStatistics" parameterType="EmployeeSalesStatistics" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order`.employee_sales_statistics
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="statType != null">stat_type,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="totalSales != null">total_sales,</if>
            <if test="totalOrders != null">total_orders,</if>
            <if test="avgOrderAmount != null">avg_order_amount,</if>
            <if test="growthRate != null">growth_rate,</if>
            <if test="leaderId != null">leader_id,</if>
            <if test="leaderNickName != null">leader_nick_name,</if>
            <if test="parentLeaderId != null">parent_leader_id,</if>
            <if test="parentLeaderNickName != null">parent_leader_nick_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="statType != null">#{statType},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="totalSales != null">#{totalSales},</if>
            <if test="totalOrders != null">#{totalOrders},</if>
            <if test="avgOrderAmount != null">#{avgOrderAmount},</if>
            <if test="growthRate != null">#{growthRate},</if>
            <if test="leaderId != null">#{leaderId},</if>
            <if test="leaderNickName != null">#{leaderNickName},</if>
            <if test="parentLeaderId != null">#{parentLeaderId},</if>
            <if test="parentLeaderNickName != null">#{parentLeaderNickName},</if>
         </trim>
    </insert>

    <update id="updateEmployeeSalesStatistics" parameterType="EmployeeSalesStatistics">
        update `wendao101-order`.employee_sales_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="statType != null">stat_type = #{statType},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="totalSales != null">total_sales = #{totalSales},</if>
            <if test="totalOrders != null">total_orders = #{totalOrders},</if>
            <if test="avgOrderAmount != null">avg_order_amount = #{avgOrderAmount},</if>
            <if test="growthRate != null">growth_rate = #{growthRate},</if>
            <if test="leaderId != null">leader_id = #{leaderId},</if>
            <if test="leaderNickName != null">leader_nick_name = #{leaderNickName},</if>
            <if test="parentLeaderId != null">parent_leader_id = #{parentLeaderId},</if>
            <if test="parentLeaderNickName != null">parent_leader_nick_name = #{parentLeaderNickName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteEmployeeSalesStatisticsById" parameterType="Long">
        delete from `wendao101-order`.employee_sales_statistics where id = #{id}
    </delete>

    <delete id="deleteEmployeeSalesStatisticsByIds" parameterType="String">
        delete from `wendao101-order`.employee_sales_statistics where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 